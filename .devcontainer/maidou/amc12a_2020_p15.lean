-- Proof content:
-- 1. [Problem Restatement] Find the largest distance between any cube–root of 8 and any root of (z³−8z²−8z+64). 2. [Key Idea] Factor the second polynomial to locate its (real) roots, then compare Euclidean distances from each of the three vertices of the equilateral triangle |z|=2 (the roots of z³=8) to those three real points. 3. [Proof] Roots of z³−8 = 0 A = { 2, 2 cis 120° = −1 + i√3, 2 cis 240° = −1 − i√3 }. Roots of z³−8z²−8z+64 = 0 z³−8z²−8z+64 = (z²−8)(z−8) ⇒ B = { −2√2, 2√2, 8 }. Compute squared distances (avoids square-roots until the end). (i) From −1 + i√3 to each point of B to 8: (8−(−1))² + (0−√3)² = 9² + (−√3)² = 81 + 3 = 84. to 2√2: (2√2+1)² + (−√3)² = (3.828…)² + 3 ≈ 17.65. to −2√2: (−2√2+1)² + (−√3)² = (−1.828…)² + 3 ≈ 6.34. (ii) The point −1 − i√3 is the reflection of −1 + i√3 across the real axis; the same three values occur. (iii) From 2 (real) to each point of B to 8: (8−2)² = 36. to 2√2: (2√2−2)² ≈ 0.69. to −2√2: (−2√2−2)² ≈ 23.32. The maximum squared distance found is 84, attained between −1 ± i√3 and 8. Therefore the maximum distance is √84 = 2√21. 4. [Conclusion] The greatest distance between a root of z³=8 and a root of z³−8z²−8z+64 is 2√21, option (D).
