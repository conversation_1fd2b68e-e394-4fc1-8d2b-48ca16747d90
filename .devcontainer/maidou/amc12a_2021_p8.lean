-- Proof content:
-- 1. [Problem Restatement] Find the even/odd pattern of the triple (D₍₂₀₂₁₎, D₍₂₀₂₂₎, D₍₂₀₂₃₎) for the sequence D₀=0, D₁=0, D₂=1, Dₙ=Dₙ₋₁+Dₙ₋₃ (n ≥ 3). 2. [Key Idea] Work modulo 2: the recurrence is linear over GF(2) and its parity sequence must be periodic; the period turns out to be 7. 3. [Proof] Style 1 – Direct table Let dₙ = Dₙ (mod 2). Starting with d₀=0, d₁=0, d₂=1, n : 0 1 2 3 4 5 6 dₙ : 0 0 1 1 1 0 1 Using dₙ = dₙ₋₁ + dₙ₋₃ (mod 2) one step farther gives n : 7 8 9 10 11 12 13 dₙ : 0 0 1 1 1 0 1 so the block 0 0 1 1 1 0 1 repeats; hence dₙ has period 7. Compute the required indices: 2021 ≡ 5 (mod 7) ⇒ d₂₀₂₁ = d₅ = 0 (even) 2022 ≡ 6 (mod 7) ⇒ d₂₀₂₂ = d₆ = 1 (odd) 2023 ≡ 0 (mod 7) ⇒ d₂₀₂₃ = d₀ = 0 (even) Thus (D₂₀₂₁, D₂₀₂₂, D₂₀₂₃) ≡ (0, 1, 0) mod 2 → (E, O, E). Style 2 – Characteristic polynomial Over GF(2) the recurrence is dₙ + dₙ₋₁ + dₙ₋₃ = 0 with polynomial x³ + x² + 1. Since x³ + x² + 1 is primitive in GF(2)[x], its order is 7, so any non-zero solution has period 7. The initial vector (d₂,d₁,d₀) = (1,0,0) is non-zero, giving the same 7-cycle above, and the same conclusion. 4. [Conclusion] The parities are (E, O, E), i.e. choice (C).
