-- Proof content:
-- 1. [Problem Restatement] Determine, among the five given positive rational numbers, which one is sent to a negative value by a multiplicative-additive function f that satisfies f(ab)=f(a)+f(b) for all positive rationals and f(p)=p for every prime p. 2. [Key Idea] Because f turns products into sums, its value on any rational equals the (signed) sum of the primes appearing in its numerator and denominator, each prime counted once per exponent. 3. [Proof] Let a positive rational x be written uniquely as x = p₁^{α₁} … p_k^{α_k} / q₁^{β₁} … q_m^{β_m}, α_i , β_j ∈ ℕ. Using f(ab)=f(a)+f(b) repeatedly and f(p)=p, we get f(x)=α₁p₁+⋯+α_k p_k − (β₁q₁+⋯+β_m q_m). Compute f(x) for each candidate. A. x = 17 / 2⁵ f(x)=17 − 5·2 = 17 − 10 = 7 > 0. B. x = 11 / 2⁴ f(x)=11 − 4·2 = 11 − 8 = 3 > 0. C. x = 7 / 3² f(x)=7 − 2·3 = 7 − 6 = 1 > 0. D. x = 7 / (2·3) f(x)=7 − 2 − 3 = 2 > 0. E. x = 5² / 11 f(x)=2·5 − 11 = 10 − 11 = −1 < 0. Only option E yields a negative value. 4. [Conclusion] f(x) is negative precisely for x = 25⁄11, choice (E).
