-- Proof content:
-- 1. [Problem Restatement] Pick two distinct primes from {5, 7, 11, 13, 17}; which listed number can equal (product) – (sum)? 2. [Key Idea] Write pq – (p+q) = (p–1)(q–1)–1; because the primes are odd (p–1)(q–1) is a multiple of 4, so the value is ≡ 3 (mod 4) and is at most 191. 3. [Proof] Let p<q be the chosen primes. (i) Congruence test pq – (p+q)= (p–1)(q–1)–1. Since p, q are odd, p–1 and q–1 are even, hence (p–1)(q–1) ≡ 0 (mod 4). Therefore pq – (p+q) ≡ –1 ≡ 3 (mod 4). From the options: 22 ≡ 2 (mod 4), 60 ≡ 0, 119 ≡ 3, 180 ≡ 0, 231 ≡ 3. Only 119 and 231 pass this test. (ii) Size bound The largest possible primes are 13 and 17, giving (p–1)(q–1)–1 = 16·12 – 1 = 192 – 1 = 191. Any candidate must therefore be ≤191; 231 is too large. Thus 119 is the sole remaining possibility. (iii) Verification Take p = 11, q = 13: pq – (p+q) = 143 – 24 = 119. 4. [Conclusion] The only attainable value is 119, option (C).
