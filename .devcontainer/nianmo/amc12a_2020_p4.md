# AMC 12A 2020 Problem 4 - Proof Tree

## Problem Statement
Count the 4-digit integers whose digits are all even and that are divisible by 5.

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that exactly 100 four-digit integers have all even digits and are divisible by 5
**Parent Node**: None
**Status**: [ROOT]

### STRATEGY_001 [STRATEGY]
**Goal**: Use digit-by-digit counting with divisibility constraint
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Analyze divisibility by 5 constraint (units digit must be 0 or 5)
2. Apply even digit constraint to eliminate 5 as units digit
3. Count valid choices for each digit position
4. Apply multiplication principle
**Strategy**: Constructive counting with constraint analysis
**Status**: [TO_EXPLORE]

### SUBGOAL_001 [SUBGOAL]
**Goal**: Determine valid units digit (d₄)
**Parent Node**: STRATEGY_001
**Detailed Plan**: Prove that d₄ = 0 is the only choice satisfying both divisibility by 5 and even digit constraints
**Strategy**: Use case analysis: n % 5 = 0 implies n % 10 ∈ {0, 5}, but even constraint eliminates 5
**Status**: [PROMISING]

### SUBGOAL_002 [SUBGOAL]
**Goal**: Count valid thousands digit choices (d₁)
**Parent Node**: STRATEGY_001
**Detailed Plan**: Prove d₁ ∈ {2,4,6,8} giving exactly 4 choices
**Strategy**: Apply non-zero and even constraints
**Status**: [TO_EXPLORE]

### SUBGOAL_003 [SUBGOAL]
**Goal**: Count valid hundreds digit choices (d₂)
**Parent Node**: STRATEGY_001
**Detailed Plan**: Prove d₂ ∈ {0,2,4,6,8} giving exactly 5 choices
**Strategy**: Apply even digit constraint only
**Status**: [TO_EXPLORE]

### SUBGOAL_004 [SUBGOAL]
**Goal**: Count valid tens digit choices (d₃)
**Parent Node**: STRATEGY_001
**Detailed Plan**: Prove d₃ ∈ {0,2,4,6,8} giving exactly 5 choices
**Strategy**: Apply even digit constraint only
**Status**: [TO_EXPLORE]

### SUBGOAL_005 [SUBGOAL]
**Goal**: Apply multiplication principle
**Parent Node**: STRATEGY_001
**Detailed Plan**: Prove total count = 4 × 5 × 5 × 1 = 100
**Strategy**: Use independence of digit choices and multiplication principle
**Status**: [TO_EXPLORE]
