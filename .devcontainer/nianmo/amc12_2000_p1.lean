-- Proof content:
-- 1. [Problem Restatement] Find three distinct positive integers whose product is 2001 and whose sum is as large as possible. 2. [Key Idea] With a fixed product, the more unequal the factors, the larger their sum; inserting the factor 1 therefore maximizes the total, after which we choose the two remaining (distinct) factors of 2001 whose own sum is largest. 3. [Proof] Step 1 – Prime factorisation 2001 = 3 × 23 × 29. Step 2 – Why one factor should be 1 Suppose the optimal triple (a,b,c) has a ≥ b ≥ c ≥ 1 and abc = 2001. If c > 1, replace (a,b,c) by (ac,b,1). Because ac > a and b is unchanged, the new sum exceeds the old one, contradicting optimality. Hence c = 1 for any maximal sum, i.e. one factor must be 1. Step 3 – Maximise the remaining pair We now need two distinct factors of 2001 whose product is 2001 and whose sum is largest: • 3 × 667 sum = 3 + 667 = 670 • 23 × 87 sum = 23 + 87 = 110 • 29 × 69 sum = 29 + 69 = 98 • 1 × 2001 is forbidden because factors must be distinct. The pair (3,667) wins. Step 4 – Assemble the triple (<PERSON>,<PERSON>,<PERSON>) = 1, 3, 667 (in any order) Sum = 1 + 3 + 667 = 671. No other triple can exceed this, so the maximum possible sum is 671. 4. [Conclusion] The largest attainable value of I + M + O is 671, i.e. choice (E).
