import Mathlib.Analysis.MeanInequalities
import Mathlib.Data.Real.Basic
import Mathlib.Data.Finset.Basic
import Mathlib.Data.Real.Sqrt

theorem n_rpow_inv_le_two_sub_inv (n : ℕ) (hn : 0 < n) :
  (n : ℝ) ^ ((1 : ℝ) / n) ≤ 2 - (1 : ℝ) / n := by
  -- Set up the multiset with (n-1) ones and one n
  have h1 : (n : ℝ) > 0 := Nat.cast_pos.mpr hn
  -- Calculate arithmetic mean A = (2n-1)/n = 2-1/n
  have arith_mean : ((n - 1 : ℝ) * 1 + n) / n = 2 - 1 / n := by
    have hn_ne_zero : (n : ℝ) ≠ 0 := ne_of_gt h1
    field_simp [hn_ne_zero]
    ring
  -- Calculate geometric mean G = n^(1/n)
  have geom_mean : (1 ^ (n - 1 : ℝ) * n) ^ (1 / n : ℝ) = (n : ℝ) ^ (1 / n : ℝ) := by
    simp [Real.one_rpow]
  -- Apply AM-GM inequality G ≤ A
  have am_gm : (n : ℝ) ^ (1 / n : ℝ) ≤ 2 - 1 / n := by
    -- Use AM-GM with weights (n-1)/n and 1/n, values 1 and n
    have hn_ne_zero : (n : ℝ) ≠ 0 := ne_of_gt h1
    have weight_sum : ((n - 1 : ℝ) / n) + (1 / n) = 1 := by
      field_simp [hn_ne_zero]
    have weight1_nonneg : 0 ≤ (n - 1 : ℝ) / n := by
      apply div_nonneg
      · simp only [sub_nonneg]
        exact Nat.one_le_cast.mpr (Nat.one_le_iff_ne_zero.mpr (ne_of_gt hn))
      · exact le_of_lt h1
    have weight2_nonneg : 0 ≤ (1 : ℝ) / n := by
      apply div_nonneg
      · norm_num
      · exact le_of_lt h1
    have val1_nonneg : 0 ≤ (1 : ℝ) := by norm_num
    have val2_nonneg : 0 ≤ (n : ℝ) := le_of_lt h1
    have amgm_result := Real.geom_mean_le_arith_mean2_weighted weight1_nonneg weight2_nonneg val1_nonneg val2_nonneg weight_sum
    -- This gives us: 1^((n-1)/n) * n^(1/n) ≤ ((n-1)/n) * 1 + (1/n) * n
    rw [Real.one_rpow] at amgm_result
    simp only [one_mul, mul_one] at amgm_result
    -- Simplify the right side: (n-1)/n + (1/n) * n = (n-1)/n + 1 = 2 - 1/n
    have rhs_simp : ((n - 1 : ℝ) / n) + (1 / n) * n = 2 - 1 / n := by
      field_simp [hn_ne_zero]
      ring
    rw [rhs_simp] at amgm_result
    exact amgm_result
  -- Conclude n^(1/n) ≤ 2-1/n
  exact am_gm
