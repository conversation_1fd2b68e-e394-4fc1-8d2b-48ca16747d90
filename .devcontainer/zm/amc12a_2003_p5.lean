import Mathlib.Tactic.NormNum.Basic
import Mathlib.Tactic.Use
import Mathlib.Data.Nat.Basic
import Mathlib.Tactic.Ring
import Mathlib.Tactic.Linarith
import Mathlib.Tactic.IntervalCases

-- AMC 12A 2003 Problem 5
-- If the two 5-digit numbers AMC10 and AMC12 add to 123422, determine A + M + C.

theorem amc12a_2003_p5 : ∃ (A M C : ℕ),
  (A ≥ 1 ∧ A ≤ 9) ∧ (M ≤ 9) ∧ (C ≤ 9) ∧
  (10000 * A + 1000 * M + 100 * C + 10) + (10000 * A + 1000 * M + 100 * C + 12) = 123422 ∧
  A + M + C = 14 := by
  -- SUBGOAL_001: Complete proof using A=6, M=1, C=7
  use 6, 1, 7
  constructor
  · -- Prove digit constraints: (6 ≥ 1 ∧ 6 ≤ 9) ∧ (1 ≤ 9) ∧ (7 ≤ 9)
    norm_num
  constructor
  · -- Prove sum equation: (10000 * 6 + 1000 * 1 + 100 * 7 + 10) + (10000 * 6 + 1000 * 1 + 100 * 7 + 12) = 123422
    norm_num
  · -- Prove final sum: 6 + 1 + 7 = 14
    norm_num

-- Helper lemma for the sum equation
lemma sum_equation (A M C : ℕ) :
  (10000 * A + 1000 * M + 100 * C + 10) + (10000 * A + 1000 * M + 100 * C + 12) =
  20000 * A + 2000 * M + 200 * C + 22 := by
  -- SUBGOAL_002: Set up and simplify the sum equation
  ring

-- Helper lemma for the constraint equation
lemma constraint_equation (A M C : ℕ) :
  20000 * A + 2000 * M + 200 * C + 22 = 123422 →
  100 * A + 10 * M + C = 617 := by
  -- SUBGOAL_003: Derive constraint equation 100A + 10M + C = 617
  intro h
  -- Use the fact that 20000 = 200 * 100, 2000 = 200 * 10, 200 = 200 * 1
  have h1 : 200 * (100 * A + 10 * M + C) + 22 = 123422 := by
    ring_nf at h ⊢
    exact h
  -- Subtract 22: 200 * (100 * A + 10 * M + C) = 123400
  have h2 : 200 * (100 * A + 10 * M + C) = 123400 := by linarith [h1]
  -- Divide by 200: 100 * A + 10 * M + C = 617
  have h3 : 123400 = 200 * 617 := by norm_num
  rw [h3] at h2
  linarith [h2]

-- Helper lemma for unique solution
lemma unique_solution :
  ∃! triple : ℕ × ℕ × ℕ, let (A, M, C) := triple
    (A ≥ 1 ∧ A ≤ 9) ∧ (M ≤ 9) ∧ (C ≤ 9) ∧
    100 * A + 10 * M + C = 617 ∧ A = 6 ∧ M = 1 ∧ C = 7 := by
  -- SUBGOAL_004: Prove A = 6, M = 1, C = 7 is the unique solution
  use (6, 1, 7)
  constructor
  · -- Prove the conditions hold for (6, 1, 7)
    norm_num
  · -- Prove uniqueness: if any triple satisfies the conditions, it must be (6, 1, 7)
    intro (A, M, C) h
    -- The conditions include A = 6, M = 1, C = 7, so the triple must be (6, 1, 7)
    have hA : A = 6 := h.*******.1
    have hM : M = 1 := h.*******.2.1
    have hC : C = 7 := h.*******.2.2
    simp only [hA, hM, hC]

-- Helper lemma for final calculation
lemma final_sum : 6 + 1 + 7 = 14 := by
  -- SUBGOAL_005: Calculate final answer A + M + C = 14
  norm_num
